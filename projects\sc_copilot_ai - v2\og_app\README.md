# Supply Chain AI Dashboard

A modern, AI-powered supply chain automation dashboard built with Next.js and Aceternity UI components. This dashboard provides real-time analytics, AI assistance, and comprehensive supply chain insights.

## Features

- 🤖 **AI Assistant**: Interactive AI agent for supply chain analysis
- 📊 **Real-time Analytics**: Live charts and metrics
- 🎨 **Modern UI**: Glass morphism design with smooth animations
- 📱 **Responsive Design**: Works on all device sizes
- ⚡ **Performance Optimized**: Built with Next.js 14

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with custom animations
- **Charts**: Recharts for data visualization
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **TypeScript**: Full type safety

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd supply-chain-dashboard
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Main dashboard page
├── components/
│   ├── ui/                # Reusable UI components
│   │   └── card.tsx       # Card component
│   └── dashboard/         # Dashboard-specific components
│       ├── sidebar.tsx    # Navigation sidebar
│       ├── header.tsx     # Top header
│       ├── ai-assistant.tsx    # AI assistant widget
│       ├── sales-chart.tsx     # Sales analytics chart
│       ├── donut-chart.tsx     # Donut chart component
│       ├── credit-score.tsx    # Credit score widget
│       └── revenue-trend.tsx   # Revenue trend chart
├── lib/
│   └── utils.ts           # Utility functions
└── public/                # Static assets
```

## Components Overview

### AI Assistant
- Interactive AI brain animation
- Neural network visual effects
- Floating particles animation
- Call-to-action button

### Sales Chart
- Bar chart showing sales data
- Gradient fills and animations
- Responsive design

### Revenue Trend
- Line chart with trend analysis
- Summary statistics
- Diagonal pattern overlays

### Credit Score
- Circular progress indicator
- Gradient stroke effects
- Real-time score display

## Customization

### Colors
The dashboard uses a dark theme with blue and purple accents. You can customize colors in:
- `tailwind.config.js` - Theme colors
- `app/globals.css` - CSS custom properties

### Charts
Chart data can be modified in each component file:
- `sales-chart.tsx` - Sales data array
- `revenue-trend.tsx` - Revenue data array
- `donut-chart.tsx` - Pie chart data

### Animations
Framer Motion animations can be customized in:
- `ai-assistant.tsx` - AI brain animations
- Individual components for hover effects

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
```bash
npm run build
npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.