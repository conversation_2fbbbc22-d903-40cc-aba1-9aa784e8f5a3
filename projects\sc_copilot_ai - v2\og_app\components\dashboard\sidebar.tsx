'use client'

import React from 'react'
import { 
  LayoutDashboard, 
  TrendingUp, 
  Search, 
  FileText, 
  Settings,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'

const sidebarItems = [
  { icon: LayoutDashboard, label: 'Dashboard', active: true },
  { icon: TrendingUp, label: 'Analytics' },
  { icon: Search, label: 'Search' },
  { icon: FileText, label: 'Reports' },
  { icon: Settings, label: 'Settings' },
]

export function Sidebar() {
  return (
    <div className="w-16 bg-slate-900/50 backdrop-blur-xl border-r border-slate-800 flex flex-col items-center py-6 space-y-6">
      {/* Logo */}
      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
        <Zap className="w-6 h-6 text-white" />
      </div>
      
      {/* Navigation Items */}
      <nav className="flex flex-col space-y-4">
        {sidebarItems.map((item, index) => (
          <button
            key={index}
            className={cn(
              "w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200",
              item.active 
                ? "bg-blue-500/20 text-blue-400 border border-blue-500/30" 
                : "text-slate-400 hover:text-white hover:bg-slate-800/50"
            )}
          >
            <item.icon className="w-5 h-5" />
          </button>
        ))}
      </nav>
    </div>
  )
}