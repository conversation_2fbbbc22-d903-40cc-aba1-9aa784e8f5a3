'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/dashboard/header'
import { AIAssistant } from '@/components/dashboard/ai-assistant'
import { SalesChart } from '@/components/dashboard/sales-chart'
import { CreditScore } from '@/components/dashboard/credit-score'
import { DonutChart } from '@/components/dashboard/donut-chart'
import { EmailInterface } from '@/components/email/email-interface'
import SupplyChainFloatingDock from '@/components/supply-chain-floating-dock'

type ViewType = 'dashboard' | 'email' | 'inventory' | 'logistics' | 'analytics' | 'ai-chat' | 'settings'

export default function Dashboard() {
  const [currentView, setCurrentView] = useState<ViewType>('dashboard')

  // Listen for hash changes to handle navigation from floating dock
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1) // Remove the '#'
      if (hash && ['dashboard', 'email', 'inventory', 'logistics', 'analytics', 'ai-chat', 'settings'].includes(hash)) {
        setCurrentView(hash as ViewType)
      }
    }

    // Set initial view based on hash
    handleHashChange()

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange)
    return () => window.removeEventListener('hashchange', handleHashChange)
  }, [])

  const renderContent = () => {
    switch (currentView) {
      case 'email':
        return <EmailInterface />
      case 'dashboard':
      default:
        return (
          <div className="h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
            <main className="p-4 lg:p-6 h-full">
              <div className="max-w-7xl mx-auto h-full">
                <div className="grid grid-cols-12 gap-6 h-full">
                  {/* AI Assistant - Larger and Centered */}
                  <div className="col-span-12 lg:col-span-8">
                    <AIAssistant />
                  </div>

                  {/* Right Sidebar - Charts */}
                  <div className="col-span-12 lg:col-span-4 space-y-6">
                    <SalesChart />
                    <CreditScore />
                    <DonutChart />
                  </div>
                </div>
              </div>
            </main>
          </div>
        )
    }
  }

  return (
    <div className={currentView === 'dashboard' ? 'h-screen' : 'min-h-screen'}>
      {/* Header - Only show for dashboard view */}
      {currentView === 'dashboard' && <Header />}

      {/* Content */}
      {renderContent()}

      {/* Supply Chain Floating Dock - Fixed at bottom */}
      <SupplyChainFloatingDock />
    </div>
  )
}