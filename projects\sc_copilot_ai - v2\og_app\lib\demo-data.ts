// Demo data for the supply chain dashboard

export const salesData = [
  { month: 'Feb', value: 45, target: 50 },
  { month: 'Feb', value: 52, target: 55 },
  { month: 'Feb', value: 48, target: 50 },
  { month: 'Feb', value: 61, target: 60 },
  { month: 'Feb', value: 55, target: 58 },
  { month: 'Feb', value: 67, target: 65 },
  { month: 'Feb', value: 73, target: 70 },
]

export const revenueData = [
  { month: '2024', value: 1.2, trend: 20, growth: 5.2 },
  { month: 'Mar', value: 2.1, trend: 35, growth: 12.8 },
  { month: 'Apr', value: 1.8, trend: 28, growth: -3.1 },
  { month: 'May', value: 3.2, trend: 45, growth: 18.4 },
  { month: 'Jun', value: 2.8, trend: 38, growth: -2.8 },
  { month: 'Jul', value: 4.1, trend: 55, growth: 22.1 },
  { month: 'Aug', value: 3.5, trend: 48, growth: -4.2 },
  { month: 'Sep', value: 5.33, trend: 65, growth: 28.7 },
  { month: 'Oct', value: 4.8, trend: 58, growth: -6.1 },
  { month: 'Nov', value: 2.43, trend: 35, growth: -15.2 },
  { month: 'Dec', value: 1.2, trend: 22, growth: -8.9 },
  { month: '15 Jan', value: 1.8, trend: 28, growth: 3.4 },
]

export const distributionData = [
  { name: 'Domestic', value: 60, color: '#3b82f6', amount: 54446 },
  { name: 'International', value: 25, color: '#8b5cf6', amount: 22686 },
  { name: 'Online', value: 15, color: '#1e293b', amount: 13612 },
]

export const supplyChainMetrics = {
  totalSales: 90744,
  creditScore: 803,
  maxCreditScore: 850,
  averageRevenue: 2.43,
  maxRevenue: 5.33,
  minRevenue: 1.2,
  growthRate: 12.5,
  efficiency: 87.3,
  onTimeDelivery: 94.2,
  inventoryTurnover: 6.8
}

export const aiInsights = [
  "Sales performance is 15% above target for Q4",
  "Inventory levels optimized for seasonal demand",
  "Supply chain efficiency improved by 8.3% this month",
  "Recommend increasing international distribution by 12%",
  "Credit score indicates excellent financial health"
]

export const supplyChainAlerts = [
  { type: 'warning', message: 'Low inventory for Product SKU-1234', priority: 'medium' },
  { type: 'success', message: 'Delivery performance exceeded 95% target', priority: 'low' },
  { type: 'info', message: 'New supplier onboarding completed', priority: 'low' },
  { type: 'error', message: 'Delayed shipment from Supplier ABC', priority: 'high' }
]