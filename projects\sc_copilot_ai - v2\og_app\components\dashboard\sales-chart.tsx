'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { BarChart, Bar, XAxis, <PERSON>A<PERSON><PERSON>, ResponsiveContainer } from 'recharts'
import { TrendingUp } from 'lucide-react'

const salesData = [
  { month: 'Feb', value: 45 },
  { month: 'Feb', value: 52 },
  { month: 'Feb', value: 48 },
  { month: 'Feb', value: 61 },
  { month: 'Feb', value: 55 },
  { month: 'Feb', value: 67 },
  { month: 'Feb', value: 73 },
]

export function SalesChart() {
  return (
    <Card className="glass-card-square">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg glass-effect flex items-center justify-center">
              <TrendingUp className="w-4 h-4 text-violet-300" />
            </div>
            <div>
              <CardTitle className="text-sm font-medium text-white/80">Total sale</CardTitle>
              <div className="text-2xl font-bold text-white">90,744</div>
            </div>
          </div>
          <div className="w-8 h-8 rounded-full glass-effect flex items-center justify-center">
            <TrendingUp className="w-4 h-4 text-green-400" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="h-32">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={salesData}>
              <XAxis 
                dataKey="month" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: 'rgba(255, 255, 255, 0.6)' }}
              />
              <YAxis hide />
              <Bar 
                dataKey="value" 
                fill="url(#salesGradient)"
                radius={[2, 2, 0, 0]}
              />
              <defs>
                <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#8b5cf6" />
                  <stop offset="100%" stopColor="#7c3aed" />
                </linearGradient>
              </defs>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}