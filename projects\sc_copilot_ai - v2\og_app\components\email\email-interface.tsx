'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Mail,
  Search,
  Star,
  Archive,
  Trash2,
  Send,
  Paperclip,
  MoreHorizontal,
  Inbox,
  FileText,
  Edit3,
  Plus,
  X,
  Reply,
  CornerUpLeft,
  CornerUpRight,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'

interface Email {
  id: string
  from: string
  fromEmail: string
  to: string
  subject: string
  preview: string
  content: string
  timestamp: string
  isRead: boolean
  isStarred: boolean
  hasAttachment: boolean
  folder: 'inbox' | 'sent' | 'drafts' | 'archive' | 'trash'
}

const mockEmails: Email[] = [
  {
    id: '1',
    from: 'Supply Chain Manager',
    fromEmail: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Q4 Inventory Report - Action Required',
    preview: 'Please review the attached Q4 inventory report and provide feedback by EOD...',
    content: 'Dear Team,\n\nPlease review the attached Q4 inventory report and provide feedback by EOD. We need to address the low stock levels in several key categories.\n\nBest regards,\nSupply Chain Manager',
    timestamp: '2 hours ago',
    isRead: false,
    isStarred: true,
    hasAttachment: true,
    folder: 'inbox'
  },
  {
    id: '2',
    from: 'Logistics Coordinator',
    fromEmail: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Shipment Delay Notification',
    preview: 'We have been notified of a delay in shipment #SC-2024-001...',
    content: 'Hi Team,\n\nWe have been notified of a delay in shipment #SC-2024-001 due to weather conditions. New ETA is tomorrow at 3 PM.\n\nThanks,\nLogistics Team',
    timestamp: '4 hours ago',
    isRead: true,
    isStarred: false,
    hasAttachment: false,
    folder: 'inbox'
  },
  {
    id: '3',
    from: 'Vendor Relations',
    fromEmail: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'New Supplier Onboarding Complete',
    preview: 'We have successfully completed the onboarding process for ABC Manufacturing...',
    content: 'Dear Procurement Team,\n\nWe have successfully completed the onboarding process for ABC Manufacturing. They are now ready to fulfill orders.\n\nBest,\nVendor Relations',
    timestamp: '1 day ago',
    isRead: true,
    isStarred: false,
    hasAttachment: false,
    folder: 'inbox'
  }
]

export function EmailInterface() {
  const [selectedFolder, setSelectedFolder] = useState<'inbox' | 'sent' | 'drafts' | 'archive' | 'trash'>('inbox')
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null)
  const [isComposing, setIsComposing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [emails, setEmails] = useState<Email[]>(mockEmails)

  const folders = [
    { id: 'inbox', name: 'Inbox', icon: Inbox, count: emails.filter(e => e.folder === 'inbox').length },
    { id: 'sent', name: 'Sent', icon: Send, count: emails.filter(e => e.folder === 'sent').length },
    { id: 'drafts', name: 'Drafts', icon: Edit3, count: emails.filter(e => e.folder === 'drafts').length },
    { id: 'archive', name: 'Archive', icon: Archive, count: emails.filter(e => e.folder === 'archive').length },
    { id: 'trash', name: 'Trash', icon: Trash2, count: emails.filter(e => e.folder === 'trash').length },
  ]

  const filteredEmails = emails.filter(email => 
    email.folder === selectedFolder &&
    (email.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
     email.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
     email.content.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const markAsRead = (emailId: string) => {
    setEmails(prev => prev.map(email => 
      email.id === emailId ? { ...email, isRead: true } : email
    ))
  }

  const toggleStar = (emailId: string) => {
    setEmails(prev => prev.map(email => 
      email.id === emailId ? { ...email, isStarred: !email.isStarred } : email
    ))
  }

  return (
    <div className="h-screen flex bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Sidebar */}
      <div className="w-64 glass-effect border-r border-blue-500/20 p-4">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Mail className="w-5 h-5 text-white" />
          </div>
          <h1 className="text-xl font-bold text-white">Email</h1>
        </div>

        <button
          onClick={() => setIsComposing(true)}
          className="w-full mb-6 px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center gap-2 font-medium"
        >
          <Plus className="w-4 h-4" />
          Compose
        </button>

        <nav className="space-y-2">
          {folders.map((folder) => (
            <button
              key={folder.id}
              onClick={() => setSelectedFolder(folder.id as any)}
              className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-all duration-200 ${
                selectedFolder === folder.id
                  ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                  : 'text-slate-300 hover:bg-white/10 hover:text-white'
              }`}
            >
              <div className="flex items-center gap-3">
                <folder.icon className="w-4 h-4" />
                <span className="text-sm font-medium">{folder.name}</span>
              </div>
              {folder.count > 0 && (
                <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">
                  {folder.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Email List */}
        <div className="w-96 border-r border-blue-500/20 flex flex-col">
          {/* Search Bar */}
          <div className="p-4 border-b border-blue-500/20">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search emails..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-blue-500"
              />
            </div>
          </div>

          {/* Email List */}
          <div className="flex-1 overflow-y-auto">
            {filteredEmails.map((email) => (
              <motion.div
                key={email.id}
                onClick={() => {
                  setSelectedEmail(email)
                  markAsRead(email.id)
                }}
                className={`p-4 border-b border-slate-700/50 cursor-pointer hover:bg-white/5 transition-all duration-200 ${
                  selectedEmail?.id === email.id ? 'bg-blue-500/10 border-blue-500/30' : ''
                } ${!email.isRead ? 'bg-blue-500/5' : ''}`}
                whileHover={{ x: 4 }}
              >
                <div className="flex items-start gap-3">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleStar(email.id)
                    }}
                    className="mt-1"
                  >
                    <Star className={`w-4 h-4 ${email.isStarred ? 'text-yellow-400 fill-current' : 'text-slate-400'}`} />
                  </button>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <span className={`text-sm font-medium ${!email.isRead ? 'text-white' : 'text-slate-300'}`}>
                        {email.from}
                      </span>
                      <span className="text-xs text-slate-400">{email.timestamp}</span>
                    </div>
                    <h3 className={`text-sm mb-1 ${!email.isRead ? 'text-white font-medium' : 'text-slate-300'}`}>
                      {email.subject}
                    </h3>
                    <p className="text-xs text-slate-400 line-clamp-2">{email.preview}</p>
                    <div className="flex items-center gap-2 mt-2">
                      {email.hasAttachment && (
                        <Paperclip className="w-3 h-3 text-slate-400" />
                      )}
                      {!email.isRead && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Email Content */}
        <div className="flex-1 flex flex-col">
          {selectedEmail ? (
            <>
              {/* Email Header */}
              <div className="p-6 border-b border-blue-500/20">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h1 className="text-xl font-bold text-white mb-2">{selectedEmail.subject}</h1>
                    <div className="flex items-center gap-4 text-sm text-slate-300">
                      <span>From: {selectedEmail.from} &lt;{selectedEmail.fromEmail}&gt;</span>
                      <span>To: {selectedEmail.to}</span>
                      <span>{selectedEmail.timestamp}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button className="p-2 text-slate-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
                      <Reply className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-slate-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
                      <CornerUpLeft className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-slate-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
                      <CornerUpRight className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-slate-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Email Body */}
              <div className="flex-1 p-6 overflow-y-auto">
                <div className="prose prose-invert max-w-none">
                  <div className="whitespace-pre-wrap text-slate-200 leading-relaxed">
                    {selectedEmail.content}
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Mail className="w-16 h-16 text-slate-400 mx-auto mb-4" />
                <h2 className="text-xl font-medium text-slate-300 mb-2">Select an email to read</h2>
                <p className="text-slate-400">Choose an email from the list to view its contents</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Compose Modal */}
      <AnimatePresence>
        {isComposing && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="w-full max-w-2xl glass-effect rounded-lg border border-blue-500/30"
            >
              <div className="flex items-center justify-between p-4 border-b border-blue-500/20">
                <h2 className="text-lg font-semibold text-white">Compose Email</h2>
                <button
                  onClick={() => setIsComposing(false)}
                  className="p-1 text-slate-400 hover:text-white transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              <div className="p-4 space-y-4">
                <input
                  type="email"
                  placeholder="To"
                  className="w-full px-3 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-blue-500"
                />
                <input
                  type="text"
                  placeholder="Subject"
                  className="w-full px-3 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-blue-500"
                />
                <textarea
                  rows={12}
                  placeholder="Write your message..."
                  className="w-full px-3 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-blue-500 resize-none"
                />
                <div className="flex items-center justify-between">
                  <button className="flex items-center gap-2 px-3 py-2 text-slate-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
                    <Paperclip className="w-4 h-4" />
                    Attach
                  </button>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setIsComposing(false)}
                      className="px-4 py-2 text-slate-400 hover:text-white transition-colors"
                    >
                      Cancel
                    </button>
                    <button className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center gap-2">
                      <Send className="w-4 h-4" />
                      Send
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
