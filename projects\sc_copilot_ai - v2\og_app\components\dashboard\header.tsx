'use client'

import React from 'react'
import { Calendar, MessageCircle, Bell, User } from 'lucide-react'

export function Header() {
  return (
    <header className="h-16 glass-effect flex items-center justify-between px-6">
      <div className="flex items-center space-x-4">
        <h1 className="text-xl font-semibold text-white">SC Operator AI</h1>
      </div>

      <div className="flex items-center space-x-4">
        {/* Action Buttons */}
        <button className="w-10 h-10 rounded-lg glass-effect hover:bg-white/20 flex items-center justify-center text-white/70 hover:text-white transition-all duration-200">
          <Calendar className="w-5 h-5" />
        </button>

        <button className="w-10 h-10 rounded-lg glass-effect hover:bg-white/20 flex items-center justify-center text-white/70 hover:text-white transition-all duration-200">
          <MessageCircle className="w-5 h-5" />
        </button>

        <button className="w-10 h-10 rounded-lg glass-effect hover:bg-white/20 flex items-center justify-center text-white/70 hover:text-white transition-all duration-200 relative">
          <Bell className="w-5 h-5" />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full shadow-lg"></div>
        </button>

        {/* User Avatar */}
        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center shadow-lg">
          <User className="w-5 h-5 text-white" />
        </div>
      </div>
    </header>
  )
}