'use client'

import React from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts'
import { MessageSquare, TrendingUp } from 'lucide-react'

const revenueData = [
  { month: '2024', value: 1.2, trend: 20 },
  { month: 'Mar', value: 2.1, trend: 35 },
  { month: 'Apr', value: 1.8, trend: 28 },
  { month: 'May', value: 3.2, trend: 45 },
  { month: 'Jun', value: 2.8, trend: 38 },
  { month: 'Jul', value: 4.1, trend: 55 },
  { month: 'Aug', value: 3.5, trend: 48 },
  { month: 'Sep', value: 5.33, trend: 65 },
  { month: 'Oct', value: 4.8, trend: 58 },
  { month: 'Nov', value: 2.43, trend: 35 },
  { month: 'Dec', value: 1.2, trend: 22 },
  { month: '15 Jan', value: 1.8, trend: 28 },
]

export function RevenueTrend() {
  return (
    <Card className="glass-card">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg glass-effect flex items-center justify-center">
              <MessageSquare className="w-4 h-4 text-violet-300" />
            </div>
            <div>
              <CardTitle className="text-sm font-medium text-white/80">Revenue trend</CardTitle>
              <div className="text-xs text-white/60">Summary Statistics</div>
            </div>
          </div>
          <div className="w-8 h-8 rounded-full glass-effect flex items-center justify-center">
            <TrendingUp className="w-4 h-4 text-green-400" />
          </div>
        </div>
        
        {/* Stats Row */}
        <div className="flex items-center space-x-6 mt-4">
          <div className="text-center">
            <div className="text-lg font-bold text-white">1.2 <span className="text-xs text-slate-400">Mn</span></div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-white">5.33 <span className="text-xs text-slate-400">Max</span></div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-white">2.43 <span className="text-xs text-slate-400">Average</span></div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-white">1 <span className="text-xs text-slate-400">Day</span></div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-white">1 <span className="text-xs text-slate-400">Week</span></div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="h-40 relative">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={revenueData}>
              <defs>
                <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#8b5cf6" stopOpacity={0.3} />
                  <stop offset="100%" stopColor="#8b5cf6" stopOpacity={0} />
                </linearGradient>
                <pattern id="diagonalHatch" patternUnits="userSpaceOnUse" width="4" height="4">
                  <path d="M 0,4 l 4,-4 M -1,1 l 2,-2 M 3,5 l 2,-2" stroke="#c084fc" strokeWidth="1" opacity="0.3"/>
                </pattern>
              </defs>
              <XAxis 
                dataKey="month" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 10, fill: 'rgba(255, 255, 255, 0.6)' }}
              />
              <YAxis hide />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#8b5cf6"
                strokeWidth={2}
                dot={{ fill: '#8b5cf6', strokeWidth: 0, r: 3 }}
                activeDot={{ r: 4, fill: '#8b5cf6' }}
              />
            </LineChart>
          </ResponsiveContainer>
          
          {/* Diagonal pattern overlay */}
          <div className="absolute inset-0 pointer-events-none">
            <svg className="w-full h-full opacity-20">
              <defs>
                <pattern id="diagonalLines" patternUnits="userSpaceOnUse" width="8" height="8">
                  <path d="M 0,8 l 8,-8 M -2,2 l 4,-4 M 6,10 l 4,-4" stroke="#c084fc" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="60%" fill="url(#diagonalLines)" />
            </svg>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}