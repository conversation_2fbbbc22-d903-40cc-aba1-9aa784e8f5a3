"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export function HoverBorderGradient({
  children,
  containerClassName,
  className,
  as: Tag = "button",
  duration = 1,
  clockwise = true,
  ...props
}: React.PropsWithChildren<
  {
    as?: React.ElementType;
    containerClassName?: string;
    className?: string;
    duration?: number;
    clockwise?: boolean;
  } & React.HTMLAttributes<HTMLElement>
>) {
  const [hovered, setHovered] = useState<boolean>(false);

  return (
    <Tag
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      className={cn(
        "relative flex items-center justify-center rounded-full p-[2px] overflow-hidden",
        containerClassName
      )}
      {...props}
    >
      {/* Animated gradient border */}
      <motion.div
        className="absolute inset-0 rounded-full"
        style={{
          background: "linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6)",
          backgroundSize: "300% 300%",
        }}
        animate={{
          backgroundPosition: clockwise 
            ? ["0% 50%", "100% 50%", "0% 50%"]
            : ["100% 50%", "0% 50%", "100% 50%"],
        }}
        transition={{
          duration: duration * 3,
          repeat: Infinity,
          ease: "linear",
        }}
      />
      
      {/* Hover glow effect */}
      <motion.div
        className="absolute inset-0 rounded-full"
        style={{
          background: "radial-gradient(circle, rgba(59, 130, 246, 0.5) 0%, transparent 70%)",
        }}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ 
          opacity: hovered ? 1 : 0,
          scale: hovered ? 1.1 : 0.8,
        }}
        transition={{ duration: 0.3 }}
      />
      
      {/* Content container */}
      <div
        className={cn(
          "relative z-10 flex items-center justify-center rounded-full px-4 py-2 bg-black text-white",
          className
        )}
      >
        {children}
      </div>
    </Tag>
  );
}